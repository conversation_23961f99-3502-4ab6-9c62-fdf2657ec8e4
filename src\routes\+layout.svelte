<script lang="ts">
	import '../app.css';
	import favicon from '$lib/assets/favicon.svg';

	let { children } = $props();
	let mobileOpen = $state(false);
	let darkMode = $state(true);
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
</svelte:head>

<div class={darkMode ? 'bg-black text-white min-h-screen' : 'bg-white text-gray-900 min-h-screen'}>

<header class={"border-b " + (darkMode ? 'bg-black border-gray-800' : 'bg-white border-gray-200')}>
	<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
		<div class="flex h-16 items-center justify-between">
			<a href="/" class="text-lg font-semibold text-[#EE8C36]">AI Dgital Video</a>
			<button
				class={"inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-[#EE8C36] md:hidden " + (darkMode ? 'text-gray-200 hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-100')}
				aria-controls="mobile-menu"
				aria-expanded={mobileOpen}
				onclick={() => (mobileOpen = !mobileOpen)}
			>
				{#if mobileOpen}
					<svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
						<path d="M6 18L18 6M6 6l12 12" />
					</svg>
				{:else}
					<svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
						<path d="M3 6h18M3 12h18M3 18h18" />
					</svg>
				{/if}
			</button>
			<nav class="hidden items-center gap-6 md:flex">
				<a href="/pricing" class={darkMode ? 'text-gray-200 hover:text-white' : 'text-gray-700 hover:text-gray-900'}>Pricing</a>
				<a href="/blog" class={darkMode ? 'text-gray-200 hover:text-white' : 'text-gray-700 hover:text-gray-900'}>Blog</a>
				<a href="/affiliate" class={darkMode ? 'text-gray-200 hover:text-white' : 'text-gray-700 hover:text-gray-900'}>Affilate program</a>
				<a href="/login" class={darkMode ? 'text-gray-200 hover:text-white' : 'text-gray-700 hover:text-gray-900'}>Login</a>
				<button
					class={"ml-2 inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-[#EE8C36] " + (darkMode ? 'text-gray-200 hover:bg-gray-800' : 'text-gray-700 hover:bg-gray-100')}
					aria-label="Toggle background"
					onclick={() => (darkMode = !darkMode)}
				>
					{#if darkMode}
						<!-- Sun icon -->
						<svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
							<circle cx="12" cy="12" r="4" />
							<path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M4.93 19.07l1.41-1.41M17.66 6.34l1.41-1.41" />
						</svg>
					{:else}
						<!-- Moon icon -->
						<svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
							<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
						</svg>
					{/if}
				</button>

			</nav>
		</div>
	</div>
</header>
{#if mobileOpen}
	<nav id="mobile-menu" class="border-b bg-white md:hidden">
		<div class="space-y-1 px-4 pb-4 pt-2">
			<a href="/pricing" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Pricing</a>
			<a href="/blog" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Blog</a>
			<a href="/affiliate" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Affilate program</a>
			<a href="/login" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Login</a>
		</div>
	</nav>
{/if}

<main class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
	{@render children?.()}
</main>


</div>
