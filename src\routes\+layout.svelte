<script lang="ts">
	import '../app.css';
	import favicon from '$lib/assets/favicon.svg';

	let { children } = $props();
	let mobileOpen = $state(false);
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
</svelte:head>

<header class="border-b bg-white">
	<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
		<div class="flex h-16 items-center justify-between">
			<a href="/" class="text-lg font-semibold text-gray-900">AI Dgital Video</a>
			<button
				class="inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 md:hidden"
				aria-controls="mobile-menu"
				aria-expanded={mobileOpen}
				onclick={() => (mobileOpen = !mobileOpen)}
			>
				{#if mobileOpen}
					<svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
						<path d="M6 18L18 6M6 6l12 12" />
					</svg>
				{:else}
					<svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
						<path d="M3 6h18M3 12h18M3 18h18" />
					</svg>
				{/if}
			</button>
			<nav class="hidden items-center gap-6 md:flex">
				<a href="/pricing" class="text-gray-700 hover:text-gray-900">Pricing</a>
				<a href="/blog" class="text-gray-700 hover:text-gray-900">blog</a>
				<a href="/affiliate" class="text-gray-700 hover:text-gray-900">Affilate program</a>
			</nav>
		</div>
	</div>
</header>
{#if mobileOpen}
	<nav id="mobile-menu" class="border-b bg-white md:hidden">
		<div class="space-y-1 px-4 pb-4 pt-2">
			<a href="/pricing" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Pricing</a>
			<a href="/blog" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">blog</a>
			<a href="/affiliate" class="block rounded px-3 py-2 text-base text-gray-700 hover:bg-gray-50">Affilate program</a>
		</div>
	</nav>
{/if}

<main class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
	{@render children?.()}
</main>
